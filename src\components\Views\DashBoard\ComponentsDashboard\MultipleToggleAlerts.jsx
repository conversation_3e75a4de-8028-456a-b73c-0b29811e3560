import React, { useContext, useEffect, useRef, useCallback } from 'react'
import { DashboardContext } from '../../../../context/DashboardProvider'
import { UserContext } from '../../../../context/UserProvider'
import { Snackbar, Box, Typography, IconButton } from '@material-ui/core'
import { Alert } from '@material-ui/lab'
import { Close } from '@material-ui/icons'
import { db } from '../../../../config/firebase'

export const MultipleToggleAlerts = () => {
  const { usuario, currentMac } = useContext(UserContext);
  const { toggleAlerts, setToggleAlerts } = useContext(DashboardContext);
  const timeoutRefs = useRef({});
  const unsubscribeRefs = useRef({});

  // Función para limpiar timeout y listener específico
  const cleanupAlert = useCallback((alertId) => {
    if (timeoutRefs.current[alertId]) {
      clearTimeout(timeoutRefs.current[alertId]);
      delete timeoutRefs.current[alertId];
    }
    if (unsubscribeRefs.current[alertId]) {
      unsubscribeRefs.current[alertId]();
      delete unsubscribeRefs.current[alertId];
    }
  }, []);

  // Función para actualizar el estado de una alerta específica
  const updateAlert = useCallback((alertId, updates) => {
    setToggleAlerts(prevAlerts => 
      prevAlerts.map(alert => 
        alert.id === alertId ? { ...alert, ...updates } : alert
      )
    );
  }, [setToggleAlerts]);

  // Función para remover una alerta
  const removeAlert = useCallback((alertId) => {
    cleanupAlert(alertId);
    setToggleAlerts(prevAlerts => prevAlerts.filter(alert => alert.id !== alertId));
  }, [cleanupAlert, setToggleAlerts]);

  // Función para cerrar una alerta manualmente
  const handleClose = useCallback((alertId) => {
    removeAlert(alertId);
  }, [removeAlert]);

  // Función para mostrar mensaje de error cuando no hay respuesta
  const showNoResponseError = useCallback((alertId, toggleName) => {
    console.log(`❌ TIMEOUT: No se recibió respuesta del toggle ${toggleName} (outId: ${alertId})`);
    updateAlert(alertId, {
      type: "error",
      title: "Error",
      message: `${toggleName}: No se recibió respuesta del módulo. Inténtalo de nuevo más tarde.`,
      autoClose: true
    });

    // Auto-cerrar después de 6 segundos
    setTimeout(() => {
      removeAlert(alertId);
    }, 6000);
  }, [updateAlert, removeAlert]);

  // Efecto principal para manejar las alertas
  useEffect(() => {
    if (!usuario.username || currentMac === "") {
      return;
    }

    toggleAlerts.forEach(alert => {
      // Solo procesar alertas que están esperando confirmación
      if (alert.type === "info" && !timeoutRefs.current[alert.id]) {
        const path = `${usuario.username}/infoDevices/${currentMac}/${alert.canId}/fromModule`;
        const docRef = db.collection(path).doc("configOK");

        // Configurar timeout de 7 segundos
        timeoutRefs.current[alert.id] = setTimeout(() => {
          cleanupAlert(alert.id);
          showNoResponseError(alert.id, alert.toggleName);
        }, 7000);

        // Configurar listener para confirmación
        unsubscribeRefs.current[alert.id] = docRef.onSnapshot((docSnapshot) => {
          if (docSnapshot.exists) {
            const data = docSnapshot.data();
            
            // Verificar si la confirmación es para este toggle específico
            if (data.accion === 4 && 
                data.act === 'recOK' && 
                (data.kind === 5 || data.kind === 6) && 
                String(data.outid) === String(alert.outId)) {
              
              console.log(`✅ Confirmación recibida para toggle ${alert.toggleName} (outId: ${alert.outId})`);
              cleanupAlert(alert.id);

              // Actualizar alerta a éxito
              updateAlert(alert.id, {
                type: "success",
                title: "Completado",
                message: `${alert.toggleName}: El modulo se configuro correctamente`,
                autoClose: true
              });

              // Auto-cerrar después de 3 segundos y limpiar documento
              setTimeout(() => {
                removeAlert(alert.id);
                docRef.update({
                  accion: 0,
                  act: "-"
                });
              }, 3000);
            }
          }
        });
      }
    });

    // Cleanup al desmontar
    return () => {
      Object.keys(timeoutRefs.current).forEach(alertId => {
        cleanupAlert(alertId);
      });
    };
  }, [toggleAlerts, usuario.username, currentMac, cleanupAlert, showNoResponseError, updateAlert, removeAlert]);

  // Limpiar alertas cuando el usuario o MAC cambian
  useEffect(() => {
    if (!usuario.username || currentMac === "") {
      // Limpiar todas las alertas y sus listeners
      Object.keys(timeoutRefs.current).forEach(alertId => {
        cleanupAlert(alertId);
      });
      setToggleAlerts([]);
    }
  }, [usuario.username, currentMac, cleanupAlert, setToggleAlerts]);

  return (
    <Box>
      {toggleAlerts.map((alert, index) => (
        <Snackbar
          key={alert.id}
          open={true}
          anchorOrigin={{ 
            vertical: 'top', 
            horizontal: 'center' 
          }}
          style={{ 
            top: `${80 + (index * 70)}px` // Espaciar las alertas verticalmente
          }}
          autoHideDuration={alert.autoClose ? 6000 : null}
          onClose={() => handleClose(alert.id)}
        >
          <Alert 
            severity={alert.type}
            action={
              <IconButton
                size="small"
                aria-label="close"
                color="inherit"
                onClick={() => handleClose(alert.id)}
              >
                <Close fontSize="small" />
              </IconButton>
            }
          >
            <Typography variant="body2">
              {alert.message}
            </Typography>
          </Alert>
        </Snackbar>
      ))}
    </Box>
  );
};

export default MultipleToggleAlerts;
