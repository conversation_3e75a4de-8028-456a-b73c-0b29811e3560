import React, { useState, useEffect, useContext, useRef, useCallback } from "react";
import { UserContext } from "./UserProvider";
import { getAllUidConfigData } from "./functions/DashboardFunctions/getAllUidConfigData";
import { getAllTiles } from "./functions/DashboardFunctions/getAllTiles";
import { getAllNodesData } from "./functions/DashboardFunctions/sharedFunctions";

export const DashboardContext = React.createContext();

const DashboardProvider = ({ children }) => {
  const { usuario, dataMacCan,currentMac,typeOfCans,numberOfCans } = useContext(UserContext);
  const { username } = usuario;
  const [allTiles, setAllTiles] = useState([]);
  const [allUidConfigData, setAllUidConfigData] = useState([]);
  const [loraTiles, setLoraTiles] = useState(null);
  const [isDragButton, setIsDragButton] = useState(true);
  const [openLwToggleAlert, setOpenLwToggleAlert] = useState(false)
  const [lwToggleAlertData, setLwToggleAlertData] = useState({type: "info", msg: "Enviando comando"})
  const [alertStatus, setAlertStatus] = useState({
    open: false,
    type: "info",
    tittle: "Info",
    txt: "Enviando comando",
    canId: null
  })

  // Nuevo estado para manejar múltiples alertas de toggles
  const [toggleAlerts, setToggleAlerts] = useState([])
  const isMountedRef = useRef(true);

  useEffect(() => {
    // Limpiar datos cuando no hay usuario o cuando cambia el usuario
    if (!username) {
      // console.log("Limpiando datos del dashboard porque no hay usuario");
      setAllTiles([]);
      setAllUidConfigData([]);
      setLoraTiles(null);
      setIsDragButton(true);
      setOpenLwToggleAlert(false);
      setLwToggleAlertData({type: "info", msg: "Enviando comando"});
      return;
    }

    // console.log(`Cargando datos del dashboard para el usuario: ${username}`);

    const fetchLoraNodes = async () => {
      try {
        const configDataNodes = await getAllNodesData(username);
        if (isMountedRef.current) {
          setLoraTiles(configDataNodes === "No Lora Devices" ? null : configDataNodes);
        }
      } catch (error) {
        console.error("Error al cargar nodos Lora:", error);
        if (isMountedRef.current) {
          setLoraTiles(null);
        }
      }
    };

    fetchLoraNodes();

    return () => {
      isMountedRef.current = false;
    };
  }, [username]);

  const moveTileToMain = useCallback((tileId) => {
    setAllTiles(prevTiles => {
        const newTiles = [...prevTiles]; // Crea una copia del estado actual
        let tileFound = null;
        let sourceIndex = null;

        // Buscar el tile en cada sub-arreglo
        for (let i = 0; i < newTiles.length; i++) {
            tileFound = newTiles[i].find(tile => tile.uid === tileId);
            if (tileFound) {
                sourceIndex = i; // Guardar el índice del sub-arreglo donde se encontró
                break; // Salir del ciclo una vez encontrado el tile
            }
        }

        if (tileFound && sourceIndex !== null) {
            // Eliminar el tile del sub-arreglo original
            newTiles[sourceIndex] = newTiles[sourceIndex].filter(tile => tile.uid !== tileId);
            // Añadir el tile al sub-arreglo en la posición 0
            newTiles[0].push(tileFound);
        }

        return newTiles;
    });
  }, [setAllTiles]);


  return (
    <DashboardContext.Provider value={{ allTiles, setAllTiles, loraTiles, isDragButton,
    setIsDragButton, moveTileToMain, openLwToggleAlert, setOpenLwToggleAlert,
    lwToggleAlertData,setLwToggleAlertData, alertStatus, setAlertStatus, toggleAlerts, setToggleAlerts }}>
      {children}
    </DashboardContext.Provider>
  );
};

export default DashboardProvider;
