import React, { useContext, useEffect, useRef, useCallback } from 'react'
import { DashboardContext } from '../../../../context/DashboardProvider'
import { UserContext } from '../../../../context/UserProvider'
import { Snackbar } from '@material-ui/core'
import { Alert } from '@material-ui/lab'
import { db } from '../../../../config/firebase'


export const DashboardAlerts = () => {
	const { usuario, currentMac } = useContext(UserContext);
	const {alertStatus, setAlertStatus} = useContext(DashboardContext)
	const timeoutRef = useRef(null);
	const unsubscribeRef = useRef(null);
	const timeoutTriggered = useRef(false);


	const handleClose = (_, reason) => {
		if (reason === 'clickaway') {
		  return;
		}
		setAlertStatus(prevStatus => ({
			...prevStatus,
			open: false,
		}));
	};

	// Función para limpiar timeout y listener
	const cleanupTimeout = useCallback(() => {
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
			timeoutRef.current = null;
		}
		if (unsubscribeRef.current) {
			unsubscribeRef.current();
			unsubscribeRef.current = null;
		}
		timeoutTriggered.current = false; // Reiniciar la bandera
	}, []);

	// Función para mostrar mensaje de error cuando no hay respuesta
	const showNoResponseError = useCallback(() => {
		console.log("❌ TIMEOUT: No se recibió respuesta del módulo");
		setAlertStatus(prevStatus => ({
			...prevStatus,
			open: true,
			type: "error",
			tittle: "Error",
			txt: "No se recibió respuesta del módulo. Inténtalo de nuevo más tarde.",
		}));

		// Auto-cerrar después de 6 segundos
		setTimeout(() => {
			// console.log("🔄 Auto-cerrando mensaje de error");
			setAlertStatus(prevStatus => ({
				...prevStatus,
				open: false,
				canId: null
			}));
		}, 6000);
	}, [setAlertStatus]);

	

	useEffect(() => {
		cleanupTimeout();

		if (!usuario.username || currentMac === "" || alertStatus.canId === null || !alertStatus.open) {
			return;
		}
		console.log("Entre a parte 1")

		if (alertStatus.type === "info") {
			const path = `${usuario.username}/infoDevices/${currentMac}/${alertStatus.canId}/fromModule`;
			const docRef = db.collection(path).doc("configOK");
			// console.log("Esto es docRef:",docRef)

			// console.log("⏰ Configurando timeout de 5 segundos");
			timeoutRef.current = setTimeout(() => {
				// console.log("⏰ TIMEOUT ejecutándose después de 5 segundos");
				timeoutTriggered.current = true; // Activar bandera
				cleanupTimeout();
				showNoResponseError();
			}, 7000);


			unsubscribeRef.current = docRef.onSnapshot((docSnapshot) => {
				if(docSnapshot.exists) {
					const data = docSnapshot.data();
					
					if(data.accion === 4 && data.act === 'recOK' && (data.kind === 5 || data.kind === 6) && alertStatus.open === true) {
						
						// Verificar si ya se disparó el timeout
						if (timeoutTriggered.current) {
							console.log("⚠️ Confirmación recibida, pero ya se mostró error por timeout.");
							return;
						}

						// console.log("✅ Confirmación recibida - limpiando timeout");
						cleanupTimeout();

						// console.log("📢 Mostrando mensaje de éxito");
						setAlertStatus(prevStatus => ({
							...prevStatus,
							open: true,
							type: "success",
							tittle: "Completado",
							txt: "El modulo se configuro correctamente",
						}));

						setTimeout(() => {
							// console.log("🔄 Auto-cerrando mensaje de éxito");
							setAlertStatus(prevStatus => ({
								...prevStatus,
								open: false,
								canId: null
							}));
							docRef.update({
								accion: 0,
								act: "-"
							});
						}, 6000);
					}
				}
			});
		}

		return () => {
			cleanupTimeout();
		};

	}, [alertStatus.canId, alertStatus.open, alertStatus.type, currentMac, usuario.username, cleanupTimeout, showNoResponseError, setAlertStatus]);


  return (
	<Snackbar
	open={alertStatus.open}
	anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
	style={{ top: '20px' }} // Posicionar arriba para no interferir con las alertas de toggles
	autoHideDuration={alertStatus.type === "info" ? null : 6000} // No auto-cerrar cuando está enviando comando
	onClose={handleClose}
	>
		<Alert onClose={handleClose} severity={alertStatus.type}>
			{alertStatus.txt}
		</Alert>
	</Snackbar>
  )
}
