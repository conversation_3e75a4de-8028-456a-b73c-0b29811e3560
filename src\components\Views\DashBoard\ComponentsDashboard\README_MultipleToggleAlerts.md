# Sistema de Alertas Múltiples para Toggles

## Descripción
Este sistema permite mostrar alertas individuales para cada toggle que envía un comando, permitiendo rastrear el estado de confirmación de cada uno por separado.

## Componentes

### 1. MultipleToggleAlerts.jsx
Componente principal que maneja múltiples alertas de toggles simultáneamente.

**Características:**
- Maneja múltiples alertas simultáneas
- Cada alerta tiene su propio timeout de 7 segundos
- Identifica cada toggle por su `outId` único
- Muestra el nombre del toggle en cada alerta
- Posiciona las alertas verticalmente para evitar superposición

### 2. Modificaciones en DiscreteOutTile.jsx
Se modificó la función `handleToggle` para usar el nuevo sistema:

```javascript
async function handleToggle(e) {
  // ... código existente ...
  
  // Agregar nueva alerta para este toggle específico
  const alertId = `${toggle.outid}_${Date.now()}`;
  const newAlert = {
    id: alertId,
    outId: toggle.outid,
    canId: toggle.canid,
    toggleName: name,
    type: "info",
    title: "Enviando comando",
    message: `${name}: ⏳ Enviando comando, por favor espera…`,
    autoClose: false
  };

  setToggleAlerts(prevAlerts => [...prevAlerts, newAlert]);
  
  await sendCommandWhenToggle(username, toggle);
}
```

### 3. Modificaciones en DashboardProvider.js
Se agregó el estado `toggleAlerts` para manejar las alertas múltiples:

```javascript
const [toggleAlerts, setToggleAlerts] = useState([])
```

## Flujo de Funcionamiento

1. **Envío de Comando:**
   - Usuario hace clic en un toggle
   - Se crea una nueva alerta con información específica del toggle
   - Se agrega a la lista de alertas activas
   - Se muestra mensaje "⏳ Enviando comando, por favor espera…"

2. **Monitoreo de Confirmación:**
   - Se configura un listener en Firebase para el documento `configOK`
   - Se establece un timeout de 7 segundos
   - Se verifica que la confirmación corresponda al toggle específico usando `outId`

3. **Confirmación Exitosa:**
   - Se recibe confirmación con `accion: 4`, `act: 'recOK'`, y `outid` coincidente
   - Se actualiza la alerta a tipo "success"
   - Se muestra mensaje "Comando ejecutado correctamente"
   - Se auto-cierra después de 3 segundos

4. **Timeout (Sin Confirmación):**
   - Si no se recibe confirmación en 7 segundos
   - Se actualiza la alerta a tipo "error"
   - Se muestra mensaje "No se recibió respuesta del módulo"
   - Se auto-cierra después de 6 segundos

## Ventajas del Nuevo Sistema

1. **Rastreo Individual:** Cada toggle tiene su propia alerta identificable
2. **Múltiples Comandos:** Permite enviar varios comandos simultáneamente
3. **Estado Claro:** Muestra claramente qué toggles han confirmado y cuáles no
4. **No Interferencia:** Las alertas no se superponen ni interfieren entre sí
5. **Información Específica:** Cada alerta muestra el nombre del toggle correspondiente

## Estructura de Datos

### Alerta Individual
```javascript
{
  id: "outId_timestamp",           // ID único de la alerta
  outId: "3",                      // ID de salida del toggle
  canId: "1",                      // ID del CAN
  toggleName: "Bomba Principal",   // Nombre del toggle
  type: "info|success|error",      // Tipo de alerta
  title: "Enviando comando",       // Título de la alerta
  message: "Bomba Principal: ⏳ Enviando comando...", // Mensaje completo
  autoClose: false                 // Si se auto-cierra o no
}
```

## Posicionamiento Visual
Las alertas se posicionan verticalmente con un espaciado de 70px entre cada una:
- Primera alerta: top: 80px
- Segunda alerta: top: 150px
- Tercera alerta: top: 220px
- etc.

Esto evita que las alertas se superpongan y permite ver el estado de múltiples toggles simultáneamente.
